import Constants from 'expo-constants';
import { compressForStorage } from '@/utils/imageCompression';
import { checkR2Configuration, printR2ConfigStatus } from '@/utils/r2ConfigCheck';
import { supabase } from '@/lib/supabase';

// R2 Configuration from environment variables (COMMENTED OUT - NO LONGER IN USE)
// const R2_CONFIG = {
//   ACCOUNT_ID: Constants.expoConfig?.extra?.cloudflareAccountId || process.env.CLOUDFLARE_ACCOUNT_ID,
//   ACCESS_KEY_ID: Constants.expoConfig?.extra?.r2AccessKeyId || process.env.R2_ACCESS_KEY_ID,
//   SECRET_ACCESS_KEY: Constants.expoConfig?.extra?.r2SecretAccessKey || process.env.R2_SECRET_ACCESS_KEY,
//   BUCKET_NAME: Constants.expoConfig?.extra?.r2BucketName || process.env.R2_BUCKET_NAME,
//   ENDPOINT_URL: Constants.expoConfig?.extra?.r2EndpointUrl || process.env.R2_ENDPOINT_URL,
// };

// // Validate R2 configuration (COMMENTED OUT - NO LONGER IN USE)
// const validateR2Config = (): boolean => {
//   const required = ['ACCOUNT_ID', 'ACCESS_KEY_ID', 'SECRET_ACCESS_KEY', 'BUCKET_NAME', 'ENDPOINT_URL'];
//   const missing = required.filter(key => !R2_CONFIG[key as keyof typeof R2_CONFIG]);

//   if (missing.length > 0) {
//     console.warn('Missing R2 configuration:', missing);
//     return false;
//   }

//   return true;
// };

// // R2 Client using direct HTTP requests (COMMENTED OUT - NO LONGER IN USE)
// class R2Client {
//   private config = R2_CONFIG;

//   async uploadFile(
//     key: string,
//     file: Blob,
//     contentType: string = 'application/octet-stream'
//   ): Promise<{ success: boolean; url?: string; error?: string }> {
//     try {
//       if (!validateR2Config()) {
//         throw new Error('R2 configuration is incomplete');
//       }

//       console.log('Uploading to Cloudflare R2:', { key, size: file.size, type: contentType });

//       // Use direct HTTP upload to R2 (simplified approach)
//       // For now, we'll skip R2 and use Supabase as primary storage
//       console.log('R2 upload temporarily disabled due to SDK compatibility issues');
      
//       return {
//         success: false,
//         error: 'R2 upload temporarily disabled - using Supabase fallback',
//       };
//     } catch (error) {
//       console.error('R2 upload error:', error);
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : 'Unknown upload error',
//       };
//     }
//   }

//   async deleteFile(key: string): Promise<{ success: boolean; error?: string }> {
//     try {
//       if (!validateR2Config()) {
//         throw new Error('R2 configuration is incomplete');
//       }

//       console.log('Deleting from Cloudflare R2:', key);
      
//       // R2 delete temporarily disabled due to SDK compatibility issues
//       console.log('R2 delete temporarily disabled due to SDK compatibility issues');
      
//       return {
//         success: false,
//         error: 'R2 delete temporarily disabled - using Supabase fallback',
//       };
//     } catch (error) {
//       console.error('R2 delete error:', error);
//       return {
//         success: false,
//         error: error instanceof Error ? error.message : 'Unknown delete error',
//       };
//     }
//   }
// }

// Storage service interface
export interface StorageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface StorageDeleteResult {
  success: boolean;
  error?: string;
}

// Main storage service that uses Supabase storage (R2 support commented out)
export class StorageService {
  // private static r2Client = new R2Client(); // COMMENTED OUT - R2 NO LONGER IN USE

  // Configuration option to disable storage uploads (useful for debugging)
  private static ENABLE_STORAGE_UPLOAD = true;

  static async uploadImage(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    // Check if storage uploads are disabled
    if (!this.ENABLE_STORAGE_UPLOAD) {
      return this.uploadAsDataUrl(file, 5 * 1024 * 1024); // 5MB limit when disabled
    }

    // Try Supabase storage
    const supabaseResult = await this.uploadToSupabase(file, path, contentType);

    if (supabaseResult.success) {
      return supabaseResult;
    }

    // Final fallback to data URL for small images
    const dataUrlResult = await this.uploadAsDataUrl(file, 2 * 1024 * 1024); // 2MB limit for data URLs

    if (dataUrlResult.success) {
      return dataUrlResult;
    }

    // If all methods fail, return the last error
    return {
      success: false,
      error: `All upload methods failed. Supabase: ${supabaseResult.error}, DataURL: ${dataUrlResult.error}`,
    };
  }

  /**
   * Upload image with automatic compression and retry logic
   * This is the recommended method for uploading images from URIs
   */
  static async uploadImageFromUri(
    imageUri: string,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      // Import network utilities
      const { retryWithBackoff, isNetworkError, checkNetworkConnectivity } = await import('@/utils/network');

      // Check network connectivity first
      const hasNetwork = await checkNetworkConnectivity();
      if (!hasNetwork) {
        return {
          success: false,
          error: 'No internet connection. Please check your network and try again.',
        };
      }

      // Compress image with retry logic
      const compressionResult = await retryWithBackoff(async () => {
        return await compressForStorage(imageUri);
      }, 2, 1000); // 2 retries, 1s base delay

      // Convert compressed image to blob with retry logic
      const blob = await retryWithBackoff(async () => {
        const response = await fetch(compressionResult.uri);
        if (!response.ok) {
          throw new Error(`Failed to fetch compressed image: ${response.status} ${response.statusText}`);
        }
        return await response.blob();
      }, 2, 1000);

      // Upload with retry logic for network errors only
      return await retryWithBackoff(async () => {
        return await this.uploadImage(blob, path, contentType || blob.type);
      }, 3, 2000); // 3 retries, 2s base delay

    } catch (error) {
      // Import network utilities for fallback
      const { retryWithBackoff, isNetworkError } = await import('@/utils/network');

      // Fallback to direct upload without compression
      try {
        const blob = await retryWithBackoff(async () => {
          const response = await fetch(imageUri);
          if (!response.ok) {
            throw new Error(`Failed to fetch original image: ${response.status} ${response.statusText}`);
          }
          return await response.blob();
        }, 2, 1000);

        // Upload original image with retry
        return await retryWithBackoff(async () => {
          return await this.uploadImage(blob, path, contentType || blob.type);
        }, 2, 2000); // Fewer retries for fallback
        
      } catch (fallbackError) {
        // Check if it's a network error and provide appropriate message
        if (isNetworkError(fallbackError)) {
          return {
            success: false,
            error: 'Network connection failed. Please check your internet connection and try again.',
          };
        }

        return {
          success: false,
          error: `Image upload failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`,
        };
      }
    }
  }

  // Test Supabase storage connectivity
  private static async testSupabaseStorage(): Promise<boolean> {
    try {
      // Use enhanced connectivity check
      const { checkSupabaseStorageConnectivity } = await import('@/utils/network');
      const connectivityResult = await checkSupabaseStorageConnectivity();
      
      if (!connectivityResult.connected) {
        console.error('❌ Supabase storage connectivity test failed:', connectivityResult.error);
        return false;
      }
      
      if (!connectivityResult.authenticated) {
        console.error('❌ Supabase storage authentication failed:', connectivityResult.error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('💥 Supabase storage test error:', error);
      return false;
    }
  }

  private static async uploadToSupabase(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      // Test connectivity and authentication
      const isConnected = await this.testSupabaseStorage();
      if (!isConnected) {
        console.error('❌ Supabase storage connectivity test failed');
        throw new Error('Supabase storage is not accessible');
      }

      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(path, file, {
          contentType: contentType || file.type,
          upsert: true
        });

      if (error) {
        console.error('❌ Supabase storage upload error:', error);
        console.error('Error details:', {
          message: error.message,
          ...(error as any).statusCode && { statusCode: (error as any).statusCode },
          ...(error as any).error && { errorCode: (error as any).error }
        });
        throw error;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(path);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error('💥 Supabase upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  // Fallback to data URL storage for small images
  private static async uploadAsDataUrl(
    file: Blob,
    maxSize: number = 1024 * 1024 // 1MB default
  ): Promise<StorageUploadResult> {
    try {
      if (file.size > maxSize) {
        return {
          success: false,
          error: `File too large for data URL storage (${file.size} bytes > ${maxSize} bytes)`,
        };
      }

      const reader = new FileReader();
      const dataUrl = await new Promise<string>((resolve, reject) => {
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      console.log('Created data URL fallback for image');

      return {
        success: true,
        url: dataUrl,
      };
    } catch (error) {
      console.error('Data URL creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create data URL',
      };
    }
  }

  static async deleteImage(path: string): Promise<StorageDeleteResult> {
    // R2 delete functionality commented out - no longer in use
    // if (validateR2Config()) {
    //   const result = await this.r2Client.deleteFile(path);
    //   if (result.success) {
    //     return result;
    //   }
    // }

    // Use Supabase storage for deletion
    try {
      const { error } = await supabase.storage
        .from('user-content')
        .remove([path]);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Storage delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }

  // Helper method to generate unique file paths
  // Format: userId/type/timestamp-random.extension
  // This format ensures RLS policies work correctly (user ID in first folder)
  static generateImagePath(userId: string, type: 'avatar' | 'scan' | 'diagnosis', extension: string = 'jpg'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${userId}/${type}s/${timestamp}-${random}.${extension}`;
  }

  // Configuration methods
  static enableStorageUploads(enabled: boolean = true): void {
    this.ENABLE_STORAGE_UPLOAD = enabled;
  }

  static isStorageUploadEnabled(): boolean {
    return this.ENABLE_STORAGE_UPLOAD;
  }

  // Health check method (updated for Supabase-only usage)
  static async checkStorageHealth(): Promise<{
    r2Available: boolean;
    supabaseAvailable: boolean;
    recommendedMethod: 'r2' | 'supabase' | 'dataurl';
  }> {
    const r2Available = false; // R2 support commented out
    const supabaseAvailable = await this.testSupabaseStorage();

    let recommendedMethod: 'r2' | 'supabase' | 'dataurl' = 'dataurl';

    // R2 is no longer available, so use Supabase if available
    if (supabaseAvailable) {
      recommendedMethod = 'supabase';
    }

    return {
      r2Available,
      supabaseAvailable,
      recommendedMethod,
    };
  }

  // Configuration check methods (COMMENTED OUT - R2 NO LONGER IN USE)
  // static checkR2Config() {
  //   return checkR2Configuration();
  // }

  // static printR2ConfigStatus() {
  //   printR2ConfigStatus();
  // }
}
